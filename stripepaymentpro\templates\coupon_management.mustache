{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template enrol_stripepaymentpro/coupon_management

    Template for coupon management interface

    Context variables required for this template:
    * coupons - Array of coupon data grouped by course
    * has_coupons - Boolean indicating if there are any coupons

    Example context (json):
    {
        "coupon_data": [
            {
                "course_name": "Course Name",
                "course_id": "prod_123",
                "has_multiple_coupons": true,
                "coupon_data": [
                    {
                        "coupon_id": "coupon_123",
                        "coupon_name": "DISCOUNT10",
                        "discount_text": "10% off",
                        "duration_text": " forever",
                        "expiry_text": ". Expiry: Never",
                        "full_description": "10% off forever. Expiry: Never",
                        "creation_date": Y-m-d H:i:s",
                    }
                ]
            }
        ],
        "has_coupons": true
    }
}}

<!-- Navigation tabs -->
<nav class='strip-coupon-navigation-section' style='margin-bottom: 20px;'>
    <button id="all_coupon_button" style='margin-right: 10px;'>{{#str}}all_coupons, enrol_stripepaymentpro{{/str}}</button>
    <button id="generate_coupons_button">{{#str}}generate_coupons, enrol_stripepaymentpro{{/str}}</button>
</nav>

<!-- Display table of courses and associated coupons -->
<section class='all-coupons-section' id='all_coupons_section' style='display: block;'>
    {{#has_coupons}}
    <!-- Search functionality -->
    <div class="search-container mb-3">
        <div class="row">
            <div class="col-md-6">
                <div class="input-group">
                    <input type="text"
                           id="coupon-search"
                           class="form-control"
                           placeholder="{{#str}}search_coupons_placeholder, enrol_stripepaymentpro{{/str}}"
                           aria-label="{{#str}}search_coupons, enrol_stripepaymentpro{{/str}}">
                    <div class="input-group-append">
                        <button class="btn btn-outline-secondary" type="button" id="clear-search">
                            <i class="fa fa-times"></i>
                        </button>
                    </div>
                </div>
                <small class="form-text text-muted">{{#str}}search_help_text, enrol_stripepaymentpro{{/str}}</small>
            </div>
        </div>
    </div>
    <div class="table-responsive">
        <table class="table table-striped" id="coupons-table">
            <thead>
                <tr>
                    <th>{{#str}}course_name, enrol_stripepaymentpro{{/str}}</th>
                    <th>{{#str}}coupon_name, enrol_stripepaymentpro{{/str}}</th>
                    <th>{{#str}}discount_amount, enrol_stripepaymentpro{{/str}}</th>
                    <th>{{#str}}actions, enrol_stripepaymentpro{{/str}}</th>
                </tr>
            </thead>
            <tbody>
                {{#coupons}}
                {{#coupons}}
                <tr class="coupon-row"
                    data-course-name="{{course_name}}"
                    data-coupon-name="{{coupon_name}}"
                    data-discount-text="{{discount_text}}"
                    data-course-id="{{course_id}}">
                    <td class="course-name-cell">
                        <div class="course-info">
                        {{#is_first_coupon}}
                            <div class="course-name-wrapper">
                                <i class="fa fa-graduation-cap mr-2"></i>
                                <span class="course-name">{{course_name}}</span>
                            </div>
                            <div class="course-actions mt-2">
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-secondary dropdown-toggle"
                                            type="button"
                                            id="dropdownMenuButton-{{course_id}}"
                                            data-toggle="dropdown"
                                            data-bs-toggle="dropdown"
                                            aria-haspopup="true"
                                            aria-expanded="false"
                                            style="font-size: 0.7em;">
                                        <i class="fa fa-ellipsis-v"></i>
                                    </button>
                                    <div class="dropdown-menu" aria-labelledby="dropdownMenuButton-{{course_id}}">
                                        <button class="dropdown-item deactivate-all-btn"
                                                data-course-id="{{course_id}}"
                                                onclick="handleDeleteAllCoupons('{{course_id}}', this)">
                                            <i class="fa fa-trash-alt mr-2"></i>{{#str}}delete_all_coupons, enrol_stripepaymentpro{{/str}}
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {{/is_first_coupon}}
                    </td>
                    <td class="coupon-name-cell">
                        <button class="copy-coupon-name btn btn-sm btn-outline-secondary"
                                data-coupon-name="{{coupon_name}}"
                                title="{{#str}}click_to_copy_coupon, enrol_stripepaymentpro{{/str}}"
                                style="font-family: monospace; padding: 4px 8px;">
                            {{coupon_name}}
                            <i class="fa fa-copy ml-1" style="font-size: 0.8em;"></i>
                        </button>
                    </td>
                    <td class="discount-cell">
                        <span class="badge badge-info">{{discount_text}}</span>
                        <span class="badge badge-secondary">{{duration_text}}</span>
                        {{#expiry_text}}
                        <br><small class="text-muted">{{expiry_text}}</small>
                        <small class="text-muted"> Created - {{creation_date}}</small>
                        {{/expiry_text}}
                    </td>
                    <td class="actions-cell">
                        <button class="btn btn-sm btn-outline-danger deactivate-coupon-btn"
                                data-course-id="{{course_id}}"
                                data-coupon-id="{{coupon_id}}"
                                title="{{#str}}deactivate_coupon, enrol_stripepaymentpro{{/str}}"
                                onclick="handleCouponDelete('{{course_id}}', '{{coupon_id}}', this)">
                            <i class="fa fa-trash"></i>
                        </button>
                    </td>
                </tr>
                {{/coupons}}
                {{/coupons}}
            </tbody>
        </table>
    </div>
    {{/has_coupons}}
    {{^has_coupons}}
    <div class="alert alert-info">
        <i class="fa fa-info-circle"></i>
        {{#str}}no_coupon_found, enrol_stripepaymentpro{{/str}}
    </div>
    {{/has_coupons}}
</section>

<!-- Form section for generating coupons -->
<section class='generate-coupon-section' id='generate_coupon_section' style='display: none;'>
    <h3>{{#str}}create_new_coupon, enrol_stripepaymentpro{{/str}}</h3>
    <div id="coupon-form-container">
        <!-- Form will be rendered by PHP and inserted here -->
    </div>
</section>

<script>
    // Initialize Bootstrap dropdowns for three-dot buttons (support both Bootstrap 4 and 5)
    const dropdownButtons = document.querySelectorAll('[data-toggle="dropdown"], [data-bs-toggle="dropdown"]');
    dropdownButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            // Close all other dropdowns first
            document.querySelectorAll('.dropdown-menu.show').forEach(menu => {
                if (menu !== this.nextElementSibling) {
                    menu.classList.remove('show');
                    menu.parentElement.querySelector('.dropdown-toggle').setAttribute('aria-expanded', 'false');
                }
            });
            
            // Toggle current dropdown
            const dropdownMenu = this.nextElementSibling;
            if (dropdownMenu && dropdownMenu.classList.contains('dropdown-menu')) {
                const isShowing = dropdownMenu.classList.contains('show');
                dropdownMenu.classList.toggle('show');
                this.setAttribute('aria-expanded', isShowing ? 'false' : 'true');
            }
        });
    });

    // Close dropdowns when clicking outside
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.dropdown')) {
            document.querySelectorAll('.dropdown-menu.show').forEach(menu => {
                menu.classList.remove('show');
                const toggle = menu.parentElement.querySelector('.dropdown-toggle');
                if (toggle) {
                    toggle.setAttribute('aria-expanded', 'false');
                }
            });
        }
    });
</script>
