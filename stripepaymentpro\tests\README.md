# Stripe Payment Pro - Testing Guide

This directory contains test files for the stripepaymentpro plugin.

## Test Files

### 1. webhook_test.php
**Browser-accessible webhook test tool** (Fixed path issues)

- **URL**: `https://yourmoodle.com/enrol/stripepaymentpro/tests/webhook_test.php`
- **Purpose**: Interactive web-based testing of webhook functionality
- **Requirements**: Admin login required
- **Note**: Path to config.php fixed to `../../../config.php`

### 1b. webhook_test_browser.php (Recommended)
**Better browser-accessible webhook test tool**

- **URL**: `https://yourmoodle.com/enrol/stripepaymentpro/webhook_test_browser.php`
- **Purpose**: Interactive web-based testing of webhook functionality
- **Requirements**: Admin login required
- **Features**:
  - Tests webhook configuration
  - Checks webhook URL accessibility
  - Validates Stripe API connectivity
  - Verifies database table structure
  - Checks webservice function registration
  - Tests plugin availability
  - Better path handling and error messages

### 2. webhook_phpunit_test.php
**PHPUnit unit tests**

- **Purpose**: Automated unit testing using Moodle's PHPUnit framework
- **Requirements**: PHPUnit setup in Moodle
- **Usage**:
  ```bash
  # Install PHPUnit (run once)
  php admin/tool/phpunit/cli/util.php --install
  
  # Run the tests
  vendor/bin/phpunit enrol/stripepaymentpro/tests/webhook_phpunit_test.php
  ```

### 3. test_dropdown.html (in parent directory)
**Standalone dropdown functionality test**

- **Purpose**: Test the three-dot dropdown button functionality
- **Usage**: Open directly in browser to test dropdown behavior

## Testing Workflow

### 1. Initial Setup Testing
1. Access `webhook_test.php` in your browser
2. Ensure all tests show green checkmarks (✅)
3. Fix any configuration issues identified

### 2. Webhook Functionality Testing
1. Create a test course with stripepaymentpro enrolment
2. Set up test Stripe API keys
3. Make a test payment using Stripe test cards:
   - ******************** - Visa (succeeds)
   - ******************** - Visa (card declined)
   - ******************** - Visa (insufficient funds)
4. Verify user gets enrolled automatically via webhook
5. Check the `enrol_stripepaymentpro` table for payment records

### 3. Automated Testing
Run PHPUnit tests to verify core functionality:
```bash
vendor/bin/phpunit enrol/stripepaymentpro/tests/webhook_phpunit_test.php
```

## Common Issues and Solutions

### Blank Screen in webhook_test.php
- **Cause**: PHP errors or missing dependencies
- **Solution**: Check PHP error logs, ensure Moodle config is correct

### Webhook Not Working
- **Check**: Webhook URL is accessible from external services
- **Check**: Webservice token is configured correctly
- **Check**: Stripe webhook endpoint is created with correct URL

### API Connection Failed
- **Check**: Stripe API keys are correct for the current mode (test/live)
- **Check**: API keys have proper permissions
- **Check**: Network connectivity to Stripe servers

### Database Errors
- **Check**: Plugin is properly installed
- **Check**: Database tables exist and have correct structure
- **Solution**: Reinstall plugin or run database upgrade

## Security Notes

- Always use test API keys during development
- Never commit real API keys to version control
- Ensure webhook endpoints are properly secured
- Test mode changes don't affect live payments

## Support

For issues with these tests:
1. Check Moodle error logs
2. Verify plugin installation
3. Ensure proper permissions
4. Contact plugin developers if needed
