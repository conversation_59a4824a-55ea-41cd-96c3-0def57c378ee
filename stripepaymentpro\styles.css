    .strip-subcription-plan-details {
        max-width: 400px;
        width: 100%;
        margin: auto;
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        gap: 0.25rem;
        margin-bottom: 0.75rem;
    }

    .strip-subcription-plan-details.details-content {
        flex-direction: column;
    }

    .strip-subcription-plan-details p {
        margin: 0;
        font-weight: 600;
    }

    button#apply {
        color: #fff;
        border: 0;
        padding: 5px 16px;
        border-radius: 0.5rem;
        font-size: 13px;
    }

    button#payButton,
    button#card-button-zero {
        color: #fff;
        border: 0;
        padding: 5px 32px;
        border-radius: 0.25rem;
        font-size: 13px;
        box-shadow: 0 0.125rem 0.25rem #645cff2e;
        width: 100%;
    }

    .displaynone {
        display: none;
    }

    .container:has(.table-striped) {
        margin: 2rem 0 0 0;
        max-width: 100%;
        padding: 0;
        overflow-x: auto;
    }

    .table-striped thead th {
        vertical-align: middle;
    }

    .table-striped .btn-secondary:not(:disabled):not(.disabled):active:focus {
        box-shadow: none;
        outline: none;
    }

    .table-striped .btn-secondary:focus,
    .btn-secondary.focus {
        outline: none;
        box-shadow: none;
    }

    .generate-coupon-section {
        display: none;
    }

    #page.drawers div[role="main"]:has(.strip-coupon-navigation-section) {
        padding: 0 !important;
    }

    .course-info {
        display: flex;
        justify-content: space-between;
    }

    .strip-coupon-navigation-section {
        display: flex;
        max-width: -moz-fit-content;
        max-width: fit-content;
        width: 100%;
        align-items: center;
        justify-content: flex-start;
        gap: 0.5rem;
        border-bottom: 1px solid rgba(0, 0, 0, 0.125);
        margin-bottom: 1rem;
    }

    .strip-coupon-navigation-section button {
        background-color: transparent;
        padding: 0.5rem;
        cursor: pointer;
        border: none;
        outline: none;
        box-shadow: none;
    }

    .strip-coupon-navigation-section button.active {
        color: #0f6cbf;
        border-bottom: 1px solid #0f6cbf;
    }

    .all-coupons-section {
        overflow: auto;
        width: 100%;
    }

    .all-coupons-section table {
        width: 100%;
    }

    .all-coupons-section table thead tr {
        padding: 0.25rem;
    }

    .all-coupons-section table thead tr th {
        padding: 0.5rem 1rem;
        border: 1px dotted #dee2e6;
    }

    .all-coupons-section table tbody tr {
        padding: 0.25rem;
    }

    .all-coupons-section table tbody tr td {
        padding: 0.5rem 1rem;
        border: 1px dotted #dee2e6;
        min-height: 53px;
    }

    .all-coupons-section table tbody tr td.coupon-list {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        gap: 0.5rem;
        flex-wrap: wrap;
    }

    .all-coupons-section table tbody tr td .coupon-name {
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        background: #ececec;
        font-size: 0.875rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 0.5rem;
    }

    .all-coupons-section table tbody tr td .coupon-name button {
        background: transparent;
        padding: 0;
        outline: none;
        border: none;
        box-shadow: none;
        font-size: 0.75rem;
        color: rgba(188, 0, 0, 0.8509803922);
    }
    .all-coupons-section table tbody tr td .deactivate-all-coupons {
        outline: none;
        border: none;
        box-shadow: none;
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
        background-color: #ececec;
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 0.5rem;
        border-radius: 0.25rem;
    }

    .all-coupons-section table tbody tr td .deactivate-all-coupons i {
        background: transparent;
        padding: 0;
        outline: none;
        border: none;
        box-shadow: none;
        font-size: 0.75rem;
        color: rgba(188, 0, 0, 0.8509803922);
    }

    /* Three-dot dropdown button styles */
    .dropdown {
        position: relative;
        display: inline-block;
    }

    .dropdown-toggle {
        background: transparent !important;
        border: 1px solid #dee2e6 !important;
        color: #6c757d !important;
        padding: 0.25rem 0.5rem !important;
        font-size: 0.875rem !important;
        line-height: 1.5 !important;
        border-radius: 0.25rem !important;
        cursor: pointer;
    }

    .dropdown-toggle:hover,
    .dropdown-toggle:focus {
        background: #f8f9fa !important;
        border-color: #adb5bd !important;
        color: #495057 !important;
        box-shadow: none !important;
    }

    .dropdown-menu {
        position: absolute;
        top: 100%;
        left: 0;
        z-index: 1000;
        display: none;
        float: left;
        min-width: 10rem;
        padding: 0.5rem 0;
        margin: 0.125rem 0 0;
        font-size: 0.875rem;
        color: #212529;
        text-align: left;
        list-style: none;
        background-color: #fff;
        background-clip: padding-box;
        border: 1px solid rgba(0, 0, 0, 0.15);
        border-radius: 0.25rem;
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.175);
    }

    .dropdown-menu.show {
        display: block;
    }

    .dropdown-item {
        display: block;
        width: 100%;
        padding: 0.25rem 1rem;
        clear: both;
        font-weight: 400;
        color: #212529;
        text-align: inherit;
        text-decoration: none;
        white-space: nowrap;
        background-color: transparent;
        border: 0;
        cursor: pointer;
    }

    .dropdown-item:hover,
    .dropdown-item:focus {
        color: #16181b;
        text-decoration: none;
        background-color: #f8f9fa;
    }

/* Enhanced enrolment page styling */
.enrol-stripepaymentpro-enrol-page .pay-btn {
    margin-top: 18px;
    width: 100%;
    background-color: #0070f3;
    color: white;
    border: none;
    padding: 12px 0;
    font-size: 16px;
    font-weight: bold;
    border-radius: 6px;
    cursor: pointer;
}

.enrol-stripepaymentpro-enrol-page .pay-btn:hover {
    background-color: #005fd1;
    filter: brightness(0.9);
}

.enrol-stripepaymentpro-enrol-page .pay-btn:disabled {
    background-color: #ccc;
    cursor: not-allowed;
}

.enrol-stripepaymentpro-enrol-page .paydetailwrapper {
    padding-top: 3rem;
    width: 60%;
    margin: auto;
}

.enrol-stripepaymentpro-enrol-page .paydetailheading {
    font-size: 1rem;
    margin: 0;
    color: #6f6f6f;
}

.enrol-stripepaymentpro-enrol-page .paydetail .heading.trial-info {
    color: #28a745;
    font-weight: 600;
    background-color: #f8f9fa;
    padding: 8px 12px;
    border-radius: 4px;
    border-left: 4px solid #28a745;
}

.enrol-stripepaymentpro-enrol-page .paydetail .price-row {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.enrol-stripepaymentpro-enrol-page .paydetail .price-row .price {
    font-size: 2.375rem;
}

.enrol-stripepaymentpro-enrol-page .paydetail .price-row .duration {
    display: flex;
    flex-direction: column;
}

.enrol-stripepaymentpro-enrol-page .paydetail ul {
    padding-top: 2.5rem;
    padding-left: 0;
    border-bottom: 0.063rem solid #eee;
}

.enrol-stripepaymentpro-enrol-page .paydetail .total {
    text-decoration: none;
    padding: 0;
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.875rem;
}

.enrol-stripepaymentpro-enrol-page .paydetail .total .details .name,
.enrol-stripepaymentpro-enrol-page .paydetail .total .price .total-price {
    font-size: 1rem;
    font-weight: 600;
}

.enrol-stripepaymentpro-enrol-page .paydetail .total .duratuion {
    color: #888787;
    font-size: 0.85rem;
}

.enrol-stripepaymentpro-enrol-page .paydetail .total .price {
    text-align: right;
}

/* Total section styling */
.enrol-stripepaymentpro-enrol-page .total {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    padding: 0.875rem 0 !important;
    border-top: 2px solid #333 !important;
    margin-top: 0.875rem !important;
    margin-bottom: 0 !important;
}

.enrol-stripepaymentpro-enrol-page .total .details {
    flex: 1;
    margin: 0;
}

.enrol-stripepaymentpro-enrol-page .total .details .name {
    font-size: 1.125rem !important;
    font-weight: 700 !important;
    margin: 0 !important;
    line-height: 1.2 !important;
}

.enrol-stripepaymentpro-enrol-page .total .price {
    text-align: right !important;
    margin: 0;
}

.enrol-stripepaymentpro-enrol-page .total .price div {
    font-size: 1.125rem !important;
    font-weight: 700 !important;
    margin: 0 !important;
    line-height: 1.2 !important;
}

/* Discount section styling */
.enrol-stripepaymentpro-enrol-page .discount-section {
    border-top: 1px solid #eee;
    padding-top: 0.875rem;
    margin-bottom: 0.875rem;
}

.enrol-stripepaymentpro-enrol-page .discount-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.875rem;
}

.enrol-stripepaymentpro-enrol-page .discount-row .details .name {
    font-size: 1rem;
    font-weight: 600;
    color: #333;
}

.enrol-stripepaymentpro-enrol-page .discount-row .details .discount-note {
    font-size: 0.875rem;
    color: #6f6f6f;
    margin-top: 0.25rem;
}

.enrol-stripepaymentpro-enrol-page .discount-row .price .discount-amount {
    font-size: 1rem;
    font-weight: 600;
    color: #00a86b;
}

.enrol-stripepaymentpro-enrol-page .paydetail .promotion-code {
    color: #1177d1;
    font-size: 1.1rem;
    border-bottom: 0.063rem solid #eee;
}

.enrol-stripepaymentpro-enrol-page .paydetail .promotion-code .couponcode-wrap {
    width: 100%;
    padding-bottom: 0;
}

.enrol-stripepaymentpro-enrol-page .total.total {
    padding-top: 1rem;
    margin-bottom: 0;
    color: #00000087;
    font-weight: 500;
}

.enrol-stripepaymentpro-enrol-page #card-button-zero {
    background-color: #28a745;
}

.enrol-stripepaymentpro-enrol-page #card-button-zero:hover {
    background-color: #218838;
}

/* Enhanced coupon input styling */
.enrol-stripepaymentpro-enrol-page .stripe-coupon-input-container {
    display: flex;
    gap: 8px;
    margin-top: 8px;
    margin-bottom: 12px;
}

.enrol-stripepaymentpro-enrol-page .stripe-coupon-input {
    flex: 1;
    padding: 10px 12px;
    border: 2px solid #e1e5e9;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
    background-color: #fff;
}

.enrol-stripepaymentpro-enrol-page .stripe-coupon-input:focus {
    outline: none;
    border-color: #0070f3;
    box-shadow: 0 0 0 3px rgba(0, 112, 243, 0.1);
}

.enrol-stripepaymentpro-enrol-page .stripe-coupon-input.error {
    border-color: #dc3545;
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

.enrol-stripepaymentpro-enrol-page .stripe-coupon-input.success {
    border-color: #28a745;
    box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);
}

.enrol-stripepaymentpro-enrol-page .stripe-coupon-apply {
    background-color: #6c757d;
    color: white;
    border: none;
    padding: 10px 16px;
    font-size: 14px;
    border-radius: 6px;
    cursor: pointer;
    white-space: nowrap;
    min-width: 100px;
    transition: background-color 0.2s ease, transform 0.1s ease;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.enrol-stripepaymentpro-enrol-page .stripe-coupon-apply:hover:not(:disabled) {
    background-color: #5a6268;
    transform: translateY(-1px);
}

.enrol-stripepaymentpro-enrol-page .stripe-coupon-apply:disabled {
    background-color: #6c757d;
    cursor: not-allowed;
    opacity: 0.7;
}

.enrol-stripepaymentpro-enrol-page .stripe-coupon-apply.loading {
    background-color: #0070f3;
}

.enrol-stripepaymentpro-enrol-page .apply-spinner {
    display: inline-block;
}

/* Enhanced message styling */
.enrol-stripepaymentpro-enrol-page .coupon-message-container {
    margin-top: 8px;
    min-height: 24px;
}

.enrol-stripepaymentpro-enrol-page .coupon-message {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 8px;
    animation: slideIn 0.3s ease-out;
}

.enrol-stripepaymentpro-enrol-page .coupon-error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.enrol-stripepaymentpro-enrol-page .coupon-success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.enrol-stripepaymentpro-enrol-page .coupon-warning {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.enrol-stripepaymentpro-enrol-page .hidden {
    display: none !important;
}

/* Animations */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.enrol-stripepaymentpro-enrol-page .spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #ffffff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s ease-in-out infinite;
    margin-right: 6px;
}

/* Accessibility and help text */
.enrol-stripepaymentpro-enrol-page .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.enrol-stripepaymentpro-enrol-page .coupon-help-text {
    font-size: 12px;
    color: #6c757d;
    margin-top: 4px;
    font-style: italic;
}

/* Focus management for better accessibility */
.enrol-stripepaymentpro-enrol-page .stripe-coupon-input:focus,
.enrol-stripepaymentpro-enrol-page #apply:focus {
    outline: 2px solid #0070f3;
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .enrol-stripepaymentpro-enrol-page .coupon-error {
        background-color: #ffffff;
        color: #000000;
        border: 2px solid #dc3545;
    }

    .enrol-stripepaymentpro-enrol-page .coupon-success {
        background-color: #ffffff;
        color: #000000;
        border: 2px solid #28a745;
    }

    .enrol-stripepaymentpro-enrol-page .coupon-warning {
        background-color: #ffffff;
        color: #000000;
        border: 2px solid #ffc107;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .enrol-stripepaymentpro-enrol-page .coupon-message,
    .enrol-stripepaymentpro-enrol-page .spinner {
        animation: none;
    }

    .enrol-stripepaymentpro-enrol-page #apply {
        transition: none;
    }
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .enrol-stripepaymentpro-enrol-page .stripe-coupon-input-container {
        flex-direction: column;
        gap: 12px;
    }

    .enrol-stripepaymentpro-enrol-page .stripe-coupon-input,
    .enrol-stripepaymentpro-enrol-page #apply {
        width: 100%;
    }

    .enrol-stripepaymentpro-enrol-page #apply {
        min-width: auto;
        padding: 12px 16px;
    }
}

/* Utility classes */
.enrol-stripepaymentpro-hidden {
    display: none !important;
}

.enrol-stripepaymentpro-payment-element {
    width: 100%;
}