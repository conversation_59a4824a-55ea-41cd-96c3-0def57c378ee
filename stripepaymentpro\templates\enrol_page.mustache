{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template enrol_stripepaymentpro/enrol_page

    Contents of the enrolment widget on the course enrolment page

    Example context (json):
    {
            'currency' => "USD",
            'currency_symbol' => "$",
            'cost' => "20",
            'total_cost' => "20",
            'sign_up_fee' => "10",
            'renewal_fee' => "10",
            'coursename' => "python 101",
            'instanceid' => "$instance->id",
            'enrolbtncolor' => "#fffff",
            'enable_coupon_section' => true,
            'has_recurring' => true,
            'interval' => "days",
            'interval_count' => "7",
            'payment_gateway_checkout' = true
    }
}}
<script src="https://js.stripe.com/v3/"></script>
{{#payment_gateway_checkout}}
<div class="enrol-stripepaymentpro-enrol-page paydetailwrapper">
    <p class="heading">{{#str}}subscribe_to_enrolment, enrol_stripepaymentpro, {{coursename}}{{/str}}</p>
        <div class="price-row">
            <div class="price" id="mainprice-{{instanceid}}">
                {{#has_recurring}}
                    {{#trial_period_days}}
                        {{currency_symbol}}{{sign_up_fee}}
                    {{/trial_period_days}}
                    {{^trial_period_days}}
                        {{currency_symbol}}{{total_cost}}
                    {{/trial_period_days}}
                {{/has_recurring}}
                {{^has_recurring}}
                    {{currency_symbol}}{{total_cost}}
                {{/has_recurring}}
            </div>
            <div class="duration">
                <span>{{#str}}due_today, enrol_stripepaymentpro{{/str}}</span>
            </div>
        </div>
        {{#has_recurring}}
        {{#trial_period_days}}
        <p class="heading trial-info">
            <strong>{{#str}}days_free, enrol_stripepaymentpro, {{trial_period_days}}{{/str}}</strong><br>
            {{#str}}then_amount_per_interval, enrol_stripepaymentpro, {"amount": "{{currency_symbol}}{{renewal_fee}}", "interval_count": "{{interval_count}}", "interval": "{{interval}}"}{{/str}}
        </p>
        {{/trial_period_days}}
        {{^trial_period_days}}
        <p class="heading">{{#str}}then_amount_every_interval, enrol_stripepaymentpro, {"amount": "{{currency_symbol}}{{renewal_fee}}", "interval_count": "{{interval_count}}", "interval": "{{interval}}"}{{/str}}</p>
        {{/trial_period_days}}
        {{/has_recurring}}

        <ul>
            {{#has_recurring}}
                {{#trial_period_days}}
                <li class="total">
                    <div class="details">
                        <div class="name">{{#str}}enrolment_in_course, enrol_stripepaymentpro, {{coursename}}{{/str}}</div>
                    </div>
                    <div class="price">
                        <div class="total-price">{{currency_symbol}}{{sign_up_fee}}</div>
                    </div>
                </li>
                {{/trial_period_days}}
                {{^trial_period_days}}
                <li class="total">
                    <div class="details">
                        <div class="name">{{#str}}enrolment_in_course, enrol_stripepaymentpro, {{coursename}}{{/str}}</div>
                    </div>
                    <div class="price">
                        <div class="total-price">{{currency_symbol}}{{sign_up_fee}}</div>
                    </div>
                </li>
                <li class="total">
                    <div class="details">
                        <div class="name">{{#str}}course_enrolment_for, enrol_stripepaymentpro, {{coursename}}{{/str}}</div>
                        <div class="duration">{{#str}}billed_every_interval, enrol_stripepaymentpro, {"interval_count": "{{interval_count}}", "interval": "{{interval}}"}{{/str}}</div>
                    </div>
                    <div class="price">
                        <div class="total-price" id="recurring-price-breakdown-{{instanceid}}" data-recurring-price>{{currency_symbol}}{{renewal_fee}}</div>
                    </div>
                </li>
                {{/trial_period_days}}
            {{/has_recurring}}
            {{^has_recurring}}
            <li class="total">
                <div class="details">
                    <div class="name">{{#str}}enrolment_in_course, enrol_stripepaymentpro, {{coursename}}{{/str}}</div>
                </div>
                <div class="price">
                    <div class="total-price">{{currency_symbol}}{{sign_up_fee}}</div>
                </div>
            </li>
            {{/has_recurring}}
        </ul>

        <div class="total">
            <div class="details">
                <div class="name">{{#str}}subtotal, enrol_stripepaymentpro{{/str}}</div>
            </div>
            <div class="price">
                <div class="total-price" id="subtotal-amount-{{instanceid}}">
                    {{#has_recurring}}
                        {{#trial_period_days}}
                            {{currency_symbol}}{{sign_up_fee}}
                        {{/trial_period_days}}
                        {{^trial_period_days}}
                            {{currency_symbol}}{{subtotal}}
                        {{/trial_period_days}}
                    {{/has_recurring}}
                    {{^has_recurring}}
                        {{currency_symbol}}{{subtotal}}
                    {{/has_recurring}}
                </div>
            </div>
        </div>

    {{#enable_coupon_section}}
        <div class="promotion-code">
        <div class="couponcode-wrap">
            <span class="couponcode-text">
                {{#str}}couponcodedescription, enrol_stripepaymentpro{{/str}}
            </span>
            <div class="stripe-coupon-input-container">
                <label for="coupon" class="sr-only">{{#str}}enter_coupon_code, enrol_stripepaymentpro{{/str}}</label>
                <input type="text"
                        id="coupon-{{instanceid}}"
                        class="stripe-coupon-input"
                        placeholder="{{#str}}enter_coupon_code, enrol_stripepaymentpro{{/str}}" />
                <button id="apply-{{instanceid}}"
                        class="stripe-coupon-apply"
                        type="button"
                        aria-describedby="coupon-message-container">
                    <span class="apply-text">{{#str}}applycode, enrol_stripepaymentpro{{/str}}</span>
                    <span class="apply-spinner hidden">
                        <span class="spinner" aria-hidden="true"></span>
                        {{#str}}coupon_verifying, enrol_stripepaymentpro{{/str}}
                    </span>
                </button>
            </div>

            <div id="coupon-help" class="coupon-help-text">
                {{#str}}coupon_help_text, enrol_stripepaymentpro{{/str}}
            </div>

            <div id="coupon-message-container" class="coupon-message-container">
                <div id="coupon-error" class="coupon-message coupon-error hidden">
                    <i class="fa fa-exclamation-triangle" aria-hidden="true"></i>
                    <span class="message-text"></span>
                </div>
                <div id="coupon-success" class="coupon-message coupon-success hidden">
                    <i class="fa fa-check-circle" aria-hidden="true"></i>
                    <span class="message-text"></span>
                </div>
                <div id="coupon-warning" class="coupon-message coupon-warning hidden">
                    <i class="fa fa-exclamation-circle" aria-hidden="true"></i>
                    <span class="message-text"></span>
                </div>
            </div>
            <div id="showmessage-{{instanceid}}"></div>
        </div>
        </div>
    {{/enable_coupon_section}}
        <div id="discountsection-{{instanceid}}" class="discount-section enrol-stripepaymentpro-hidden">
            <div class="discount-row">
                <div class="details">
                    <div class="name" id="discounttag-{{instanceid}}">{{#str}}discount, enrol_stripepaymentpro{{/str}}</div>
                    <div class="discount-note" id="discountnote-{{instanceid}}">{{#str}}discount_applied, enrol_stripepaymentpro{{/str}}</div>
                </div>
                <div class="price">
                    <div class="discount-amount" id="discountamountdisplay-{{instanceid}}">-{{currency_symbol}}0.00</div>
                </div>
            </div>
        </div>
        <div class="total" id="total-{{instanceid}}">
            <div class="details">
                <div class="name">{{#str}}total_due_today, enrol_stripepaymentpro{{/str}}</div>
            </div>
            <div class="price">
                <div id="totalamount-{{instanceid}}" class="total-amount-{{instanceid}}">{{currency_symbol}}{{total_cost}}</div>
            </div>
        </div>
    <div id="paymentresponse-{{instanceid}}" class="error-message"></div>
    <div>
        <div id="buynow-{{instanceid}}">
            <button id="enrolbutton-{{instanceid}}" class="pay-btn" type="button">
                {{#str}}buy_now, enrol_stripepaymentpro{{/str}}
            </button>
        </div>
    {{/payment_gateway_checkout}}
    {{^payment_gateway_checkout}}
        {{#is_first_instance}}
        <div id="payment-element-{{instanceid}}"></div>
        {{/is_first_instance}}
        {{^is_first_instance}}
        <div id="load-payment-button-{{instanceid}}-container">
            <button id="load-payment-button-{{instanceid}}" class="pay-btn" type="button">
                {{#str}}view_details, enrol_stripepaymentpro{{/str}}
            </button>
        </div>
        <div id="payment-element-{{instanceid}}" class="enrol-stripepaymentpro-payment-element"></div>
        {{/is_first_instance}}
    {{/payment_gateway_checkout}}
    </div>
</div>