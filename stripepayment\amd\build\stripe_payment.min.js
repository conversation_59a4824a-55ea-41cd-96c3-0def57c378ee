define(["core/ajax"],function(e){"use strict";function t(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var n=t(e);const{call:o}=n.default,s=e=>{const t=new Map;return{getelement(n){const o=`${n}-${e}`;return t.has(o)||t.set(o,document.getElementById(o)),t.get(o)},setelement(e,t){const n=this.getelement(e);n&&(n.innerHTML=t)},toggleelement(e,t){const n=this.getelement(e);n&&(n.style.display=t?"block":"none")},focuselement(e){const t=this.getelement(e);t&&t.focus()},setbutton(e,t,n,o=(t?"0.7":"1")){const s=this.getelement(e);s&&(s.disabled=t,s.textContent=n,s.style.opacity=o,s.style.cursor=t?"not-allowed":"pointer")}}};return{stripe_payment:function(e,t,n,r,c,a,l){const i=s(n);if(void 0===window.Stripe)return;const u=(e,t,n)=>{let o;switch(n){case"error":o="red";break;case"success":o="green";break;default:o="blue"}i.setelement(e,`<p style="color: ${o}; font-weight: bold;">${t}</p>`),i.toggleelement(e,!0)},m=e=>{i.setelement(e,""),i.toggleelement(e,!1)};[{id:"apply",event:"click",handler:async e=>{e.preventDefault();const s=i.getelement("coupon"),r=s?.value.trim();if(!r)return u("showmessage",c,"error"),void i.focuselement("coupon");i.setbutton("apply",!0,a);try{const e=await((e,t)=>o([{methodname:"moodle_stripepayment_applycoupon",args:{couponid:e,instanceid:t}}])[0])(r,n);if(void 0===e?.status)throw new Error("Invalid server response");t=r,i.toggleelement("coupon",!1),i.toggleelement("apply",!1),(e=>{if(e.message?u("showmessage",e.message,"error"===e.uistate?"error":"success"):m("showmessage"),i.toggleelement("enrolbutton","paid"===e.uistate),i.toggleelement("total","paid"===e.uistate),"error"!==e.uistate){if(i.toggleelement("discountsection",e.showsections.discountsection),e.showsections.discountsection&&(e.couponname&&i.setelement("discounttag",e.couponname),e.discountamount&&e.currency&&i.setelement("discountamountdisplay",`-${e.currency} ${e.discountamount}`),e.discountamount&&e.discountvalue)){const t="percentoff"===e.coupontype?`${e.discountvalue}% off`:`${e.currency} ${e.discountvalue} off`;i.setelement("discountnote",t)}if(e.status&&e.currency){const t=i.getelement("totalamount");t&&(t.textContent=`${e.currency} ${parseFloat(e.status).toFixed(2)}`)}}})(e)}catch(e){u("showmessage",e.message||"Coupon validation failed","error"),i.focuselement("coupon")}}},{id:"enrolbutton",event:"click",handler:async()=>{if(i.getelement("enrolbutton")){m("paymentresponse"),i.setbutton("enrolbutton",!0,r);try{const s=await((e,t,n)=>o([{methodname:"moodle_stripepayment_enrol",args:{userid:e,couponid:t,instanceid:n}}])[0])(e,t,n);s.error?.message?u("paymentresponse",s.error.message,"error"):"success"===s.status&&s.redirecturl?window.location.href=s.redirecturl:u("paymentresponse","Unknown error occurred during payment.","error")}catch(e){u("paymentresponse",e.message,"error")}finally{i.toggleelement("enrolbutton",!1)}}}}].forEach(({id:e,event:t,handler:n})=>{const o=i.getelement(e);o&&o.addEventListener(t,n)})}}});
